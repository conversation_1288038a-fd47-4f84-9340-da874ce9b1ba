/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.app {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header */
.header {
  background: #fff;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1000;
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.logo h2 {
  color: #2c5aa0;
  margin-bottom: 0;
  font-size: 1.5rem;
}

.logo span {
  color: #666;
  font-size: 0.9rem;
  font-weight: normal;
}

.nav-links {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-links a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-links a:hover {
  color: #2c5aa0;
}

/* Hero Section */
.hero {
  display: flex;
  align-items: center;
  min-height: 100vh;
  padding: 120px 2rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.hero-content {
  flex: 1;
  max-width: 600px;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.hero p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
  padding: 12px 30px;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background: #ff6b6b;
  color: white;
}

.btn-primary:hover {
  background: #ff5252;
  transform: translateY(-2px);
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.btn-secondary:hover {
  background: white;
  color: #667eea;
}

.hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.placeholder-image {
  width: 300px;
  height: 300px;
  background: rgba(255,255,255,0.1);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  backdrop-filter: blur(10px);
}

/* Services Section */
.services {
  padding: 80px 0;
  background: #f8f9fa;
}

.services h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #2c5aa0;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.service-card {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  text-align: center;
  transition: transform 0.3s;
}

.service-card:hover {
  transform: translateY(-5px);
}

.service-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.service-card h3 {
  color: #2c5aa0;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.service-card p {
  color: #666;
  line-height: 1.6;
}

/* About Section */
.about {
  padding: 80px 0;
  background: white;
}

.about-content {
  display: flex;
  align-items: center;
  gap: 4rem;
}

.about-text {
  flex: 1;
}

.about h2 {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  color: #2c5aa0;
}

.about p {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
  color: #666;
  line-height: 1.7;
}

.stats {
  display: flex;
  gap: 2rem;
  margin-top: 2rem;
}

.stat {
  text-align: center;
}

.stat h3 {
  font-size: 2.5rem;
  color: #ff6b6b;
  margin-bottom: 0.5rem;
}

.stat p {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

.about-image {
  flex: 1;
  display: flex;
  justify-content: center;
}

.placeholder-profile {
  width: 250px;
  height: 250px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
}

/* Contact Section */
.contact {
  padding: 80px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.contact h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.contact > p {
  font-size: 1.2rem;
  margin-bottom: 3rem;
  opacity: 0.9;
}

.contact-info {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
}

.contact-item .icon {
  font-size: 1.5rem;
}

.btn-primary.large {
  padding: 15px 40px;
  font-size: 1.2rem;
}

/* Footer */
.footer {
  background: #2c3e50;
  color: white;
  text-align: center;
  padding: 2rem 0;
}

.footer p {
  margin: 0;
  opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-links {
    gap: 1rem;
  }

  .hero {
    flex-direction: column;
    text-align: center;
    padding: 140px 1rem 2rem;
  }

  .hero h1 {
    font-size: 2rem;
  }

  .hero-buttons {
    justify-content: center;
  }

  .placeholder-image {
    width: 250px;
    height: 250px;
    margin-top: 2rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .about-content {
    flex-direction: column;
    text-align: center;
  }

  .stats {
    justify-content: center;
  }

  .contact-info {
    flex-direction: column;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .hero h1 {
    font-size: 1.8rem;
  }

  .services h2, .about h2, .contact h2 {
    font-size: 2rem;
  }

  .container {
    padding: 0 15px;
  }
}
